"""
Tests for the message validator component.
"""

import pytest
import asyncio
from datetime import datetime, timedelta

from app.preprocess.message_validator import MessageValidator


class TestMessageValidator:
    """Test cases for the MessageValidator class."""
    
    @pytest.mark.asyncio
    async def test_valid_message(self, message_validator):
        """Test validation of a valid message."""
        result = await message_validator.validate_message(12345, "Schedule a meeting tomorrow")
        
        assert result["is_valid"] is True
        assert result["sanitized_message"] == "Schedule a meeting tomorrow"
        assert len(result["errors"]) == 0
    
    @pytest.mark.asyncio
    async def test_empty_message(self, message_validator):
        """Test validation of empty message."""
        result = await message_validator.validate_message(12345, "")
        
        assert result["is_valid"] is False
        assert "empty_message" in result["errors"]
    
    @pytest.mark.asyncio
    async def test_whitespace_only_message(self, message_validator):
        """Test validation of whitespace-only message."""
        result = await message_validator.validate_message(12345, "   \n\t   ")
        
        assert result["is_valid"] is False
        assert "empty_message" in result["errors"]
    
    @pytest.mark.asyncio
    async def test_message_too_long(self, message_validator):
        """Test validation of overly long message."""
        long_message = "x" * 5000
        result = await message_validator.validate_message(12345, long_message)
        
        assert result["is_valid"] is False
        assert "message_too_long" in result["errors"]
    
    @pytest.mark.asyncio
    async def test_content_sanitization(self, message_validator):
        """Test content sanitization."""
        malicious_message = "Hello <script>alert('xss')</script> world"
        result = await message_validator.validate_message(12345, malicious_message)
        
        assert result["is_valid"] is True
        assert "<script>" not in result["sanitized_message"]
        assert "content_sanitized" in result["warnings"]
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self, message_validator):
        """Test rate limiting functionality."""
        user_id = 12345
        
        # Send messages within rate limit
        for i in range(10):
            result = await message_validator.validate_message(user_id, f"Message {i}")
            assert result["is_valid"] is True
        
        # 11th message should be rate limited
        result = await message_validator.validate_message(user_id, "Message 11")
        assert result["is_valid"] is False
        assert "rate_limit_exceeded" in result["errors"]
    
    @pytest.mark.asyncio
    async def test_spam_detection_repetitive_words(self, message_validator):
        """Test spam detection for repetitive content."""
        spam_message = "buy buy buy now now now cheap cheap cheap"
        result = await message_validator.validate_message(12345, spam_message)
        
        assert result["is_valid"] is True  # Still valid but flagged
        assert "potential_spam" in result["warnings"]
    
    @pytest.mark.asyncio
    async def test_spam_detection_excessive_caps(self, message_validator):
        """Test spam detection for excessive capital letters."""
        caps_message = "URGENT!!! BUY NOW!!! LIMITED TIME!!!"
        result = await message_validator.validate_message(12345, caps_message)
        
        assert result["is_valid"] is True  # Still valid but flagged
        assert "potential_spam" in result["warnings"]
    
    @pytest.mark.asyncio
    async def test_spam_detection_special_characters(self, message_validator):
        """Test spam detection for excessive special characters."""
        special_message = "!!!@@@###$$$%%%^^^&&&***((()))"
        result = await message_validator.validate_message(12345, special_message)
        
        assert result["is_valid"] is True  # Still valid but flagged
        assert "potential_spam" in result["warnings"]
    
    @pytest.mark.asyncio
    async def test_multiple_users_rate_limiting(self, message_validator):
        """Test that rate limiting is per-user."""
        user1 = 12345
        user2 = 67890
        
        # User 1 hits rate limit
        for i in range(11):
            await message_validator.validate_message(user1, f"Message {i}")
        
        # User 2 should still be able to send messages
        result = await message_validator.validate_message(user2, "First message")
        assert result["is_valid"] is True
    
    @pytest.mark.asyncio
    async def test_rate_limit_window_reset(self, message_validator):
        """Test that rate limit window resets properly."""
        user_id = 12345
        
        # Fill up the rate limit
        for i in range(10):
            await message_validator.validate_message(user_id, f"Message {i}")
        
        # Manually clear the rate limit (simulating time passage)
        message_validator.rate_limits[user_id] = []
        
        # Should be able to send messages again
        result = await message_validator.validate_message(user_id, "New message")
        assert result["is_valid"] is True
    
    @pytest.mark.asyncio
    async def test_javascript_url_sanitization(self, message_validator):
        """Test sanitization of JavaScript URLs."""
        malicious_message = "Click here: javascript:alert('xss')"
        result = await message_validator.validate_message(12345, malicious_message)
        
        assert result["is_valid"] is True
        assert "javascript:" not in result["sanitized_message"]
        assert "content_sanitized" in result["warnings"]
    
    @pytest.mark.asyncio
    async def test_base64_data_url_sanitization(self, message_validator):
        """Test sanitization of base64 data URLs."""
        malicious_message = "Image: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
        result = await message_validator.validate_message(12345, malicious_message)
        
        assert result["is_valid"] is True
        assert "data:" not in result["sanitized_message"] or "base64" not in result["sanitized_message"]
        assert "content_sanitized" in result["warnings"]
    
    @pytest.mark.asyncio
    async def test_normal_message_no_warnings(self, message_validator):
        """Test that normal messages don't trigger warnings."""
        normal_message = "Can you help me schedule a meeting for next Tuesday at 2 PM?"
        result = await message_validator.validate_message(12345, normal_message)
        
        assert result["is_valid"] is True
        assert len(result["warnings"]) == 0
        assert len(result["errors"]) == 0
        assert result["sanitized_message"] == normal_message
    
    @pytest.mark.asyncio
    async def test_edge_case_exactly_4000_chars(self, message_validator):
        """Test message exactly at the 4000 character limit."""
        message_4000 = "x" * 4000
        result = await message_validator.validate_message(12345, message_4000)
        
        assert result["is_valid"] is True
        assert len(result["errors"]) == 0
