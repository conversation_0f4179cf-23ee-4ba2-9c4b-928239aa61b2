"""
Tests for the calendar service component.
"""

import pytest
from datetime import datetime, timedelta

from app.services.calendar_service import CalendarService, MockCalendarProvider


class TestMockCalendarProvider:
    """Test cases for the MockCalendarProvider class."""
    
    @pytest.mark.asyncio
    async def test_create_event(self):
        """Test creating a calendar event."""
        provider = MockCalendarProvider()
        
        event_data = {
            "title": "Test Meeting",
            "start_time": "2024-01-15T14:00:00Z",
            "end_time": "2024-01-15T15:00:00Z",
            "description": "A test meeting",
            "attendees": ["<EMAIL>"]
        }
        
        result = await provider.create_event(event_data)
        
        assert result["title"] == "Test Meeting"
        assert result["start_time"] == "2024-01-15T14:00:00Z"
        assert result["end_time"] == "2024-01-15T15:00:00Z"
        assert result["status"] == "confirmed"
        assert "id" in result
        assert result["provider"] == "mock"
    
    @pytest.mark.asyncio
    async def test_get_events(self):
        """Test retrieving calendar events."""
        provider = MockCalendarProvider()
        
        # Create some test events
        event1_data = {
            "title": "Event 1",
            "start_time": "2024-01-15T10:00:00Z",
            "end_time": "2024-01-15T11:00:00Z"
        }
        event2_data = {
            "title": "Event 2",
            "start_time": "2024-01-16T14:00:00Z",
            "end_time": "2024-01-16T15:00:00Z"
        }
        
        await provider.create_event(event1_data)
        await provider.create_event(event2_data)
        
        # Get events in date range
        events = await provider.get_events("2024-01-15T00:00:00Z", "2024-01-17T00:00:00Z")
        
        assert len(events) == 2
        assert any(event["title"] == "Event 1" for event in events)
        assert any(event["title"] == "Event 2" for event in events)
    
    @pytest.mark.asyncio
    async def test_update_event(self):
        """Test updating a calendar event."""
        provider = MockCalendarProvider()
        
        # Create an event
        event_data = {
            "title": "Original Title",
            "start_time": "2024-01-15T14:00:00Z",
            "end_time": "2024-01-15T15:00:00Z"
        }
        
        created_event = await provider.create_event(event_data)
        event_id = created_event["id"]
        
        # Update the event
        update_data = {
            "title": "Updated Title",
            "start_time": "2024-01-15T15:00:00Z"
        }
        
        updated_event = await provider.update_event(event_id, update_data)
        
        assert updated_event["title"] == "Updated Title"
        assert updated_event["start_time"] == "2024-01-15T15:00:00Z"
        assert updated_event["end_time"] == "2024-01-15T15:00:00Z"  # Unchanged
        assert "updated_at" in updated_event
    
    @pytest.mark.asyncio
    async def test_update_nonexistent_event(self):
        """Test updating a non-existent event."""
        provider = MockCalendarProvider()
        
        with pytest.raises(ValueError, match="Event not found"):
            await provider.update_event("nonexistent_id", {"title": "New Title"})
    
    @pytest.mark.asyncio
    async def test_delete_event(self):
        """Test deleting a calendar event."""
        provider = MockCalendarProvider()
        
        # Create an event
        event_data = {
            "title": "To Be Deleted",
            "start_time": "2024-01-15T14:00:00Z",
            "end_time": "2024-01-15T15:00:00Z"
        }
        
        created_event = await provider.create_event(event_data)
        event_id = created_event["id"]
        
        # Verify event exists
        events = await provider.get_events("2024-01-15T00:00:00Z", "2024-01-16T00:00:00Z")
        assert len(events) == 1
        
        # Delete the event
        result = await provider.delete_event(event_id)
        assert result is True
        
        # Verify event is deleted
        events = await provider.get_events("2024-01-15T00:00:00Z", "2024-01-16T00:00:00Z")
        assert len(events) == 0
    
    @pytest.mark.asyncio
    async def test_delete_nonexistent_event(self):
        """Test deleting a non-existent event."""
        provider = MockCalendarProvider()
        
        result = await provider.delete_event("nonexistent_id")
        assert result is False


class TestCalendarService:
    """Test cases for the CalendarService class."""
    
    @pytest.mark.asyncio
    async def test_create_event_with_mock_provider(self, calendar_service):
        """Test creating an event using the calendar service."""
        event_data = {
            "title": "Service Test Meeting",
            "start_time": "2024-01-15T14:00:00Z",
            "end_time": "2024-01-15T15:00:00Z",
            "description": "Testing calendar service"
        }
        
        result = await calendar_service.create_event("mock", event_data)
        
        assert result["title"] == "Service Test Meeting"
        assert result["status"] == "confirmed"
        assert "id" in result
    
    @pytest.mark.asyncio
    async def test_create_event_unknown_provider(self, calendar_service):
        """Test creating an event with unknown provider."""
        event_data = {
            "title": "Test Meeting",
            "start_time": "2024-01-15T14:00:00Z",
            "end_time": "2024-01-15T15:00:00Z"
        }
        
        with pytest.raises(ValueError, match="Unknown calendar provider"):
            await calendar_service.create_event("unknown_provider", event_data)
    
    @pytest.mark.asyncio
    async def test_find_available_slots(self, calendar_service):
        """Test finding available time slots."""
        # Create some existing events first
        event_data = {
            "title": "Existing Meeting",
            "start_time": "2024-01-15T10:00:00Z",
            "end_time": "2024-01-15T11:00:00Z"
        }
        await calendar_service.create_event("mock", event_data)
        
        # Find available slots
        available_slots = await calendar_service.find_available_slots(
            "mock", 
            60,  # 60 minutes
            "2024-01-15T09:00:00Z",
            "2024-01-15T17:00:00Z"
        )
        
        assert len(available_slots) > 0
        
        # Verify slots don't conflict with existing event
        for slot in available_slots:
            slot_start = datetime.fromisoformat(slot["start_time"].replace('Z', '+00:00'))
            slot_end = datetime.fromisoformat(slot["end_time"].replace('Z', '+00:00'))
            existing_start = datetime.fromisoformat("2024-01-15T10:00:00+00:00")
            existing_end = datetime.fromisoformat("2024-01-15T11:00:00+00:00")
            
            # No overlap
            assert slot_end <= existing_start or slot_start >= existing_end
    
    @pytest.mark.asyncio
    async def test_check_conflicts(self, calendar_service):
        """Test conflict detection."""
        # Create an existing event
        event_data = {
            "title": "Existing Meeting",
            "start_time": "2024-01-15T14:00:00Z",
            "end_time": "2024-01-15T15:00:00Z"
        }
        await calendar_service.create_event("mock", event_data)
        
        # Check for conflicts with overlapping time
        conflicts = await calendar_service.check_conflicts(
            "mock",
            "2024-01-15T14:30:00Z",  # Overlaps with existing event
            "2024-01-15T15:30:00Z"
        )
        
        assert len(conflicts) == 1
        assert conflicts[0]["title"] == "Existing Meeting"
        assert conflicts[0]["conflict_type"] == "overlap"
    
    @pytest.mark.asyncio
    async def test_no_conflicts(self, calendar_service):
        """Test when there are no conflicts."""
        # Create an existing event
        event_data = {
            "title": "Existing Meeting",
            "start_time": "2024-01-15T14:00:00Z",
            "end_time": "2024-01-15T15:00:00Z"
        }
        await calendar_service.create_event("mock", event_data)
        
        # Check for conflicts with non-overlapping time
        conflicts = await calendar_service.check_conflicts(
            "mock",
            "2024-01-15T16:00:00Z",  # No overlap
            "2024-01-15T17:00:00Z"
        )
        
        assert len(conflicts) == 0
    
    @pytest.mark.asyncio
    async def test_working_hours_filtering(self, calendar_service):
        """Test that available slots respect working hours."""
        available_slots = await calendar_service.find_available_slots(
            "mock",
            60,  # 60 minutes
            "2024-01-15T00:00:00Z",  # Start from midnight
            "2024-01-15T23:59:59Z",  # End at midnight
            working_hours_only=True
        )
        
        # All slots should be within working hours (9 AM - 5 PM)
        for slot in available_slots:
            slot_start = datetime.fromisoformat(slot["start_time"].replace('Z', '+00:00'))
            assert 9 <= slot_start.hour < 17
            # Should be weekday (Monday = 0, Sunday = 6)
            assert slot_start.weekday() < 5
    
    @pytest.mark.asyncio
    async def test_fallback_to_mock_provider(self, calendar_service):
        """Test fallback to mock provider when specified provider doesn't exist."""
        available_slots = await calendar_service.find_available_slots(
            "nonexistent_provider",
            60,
            "2024-01-15T09:00:00Z",
            "2024-01-15T17:00:00Z"
        )
        
        # Should still return results using mock provider
        assert len(available_slots) > 0
