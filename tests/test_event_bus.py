"""
Tests for the event bus system.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock

from app.utils.event_bus import EventBus, BaseEvent
from app.utils.events import MessageReceived


class TestEvent(BaseEvent):
    """Test event for testing purposes."""
    def __init__(self, data: str):
        self.data = data


class TestEventBus:
    """Test cases for the EventBus class."""
    
    @pytest.mark.asyncio
    async def test_subscribe_and_publish(self, event_bus):
        """Test basic subscription and publishing."""
        received_events = []
        
        @event_bus.subscribe("TestEvent")
        async def test_handler(event):
            received_events.append(event)
        
        # Subscribe the handler
        await event_bus.subscribe_tagged_methods(self)
        
        # Publish an event
        test_event = TestEvent("test_data")
        await event_bus.publish(test_event)
        
        # Wait for event processing
        await asyncio.sleep(0.1)
        
        # Verify event was received
        assert len(received_events) == 1
        assert received_events[0].data == "test_data"
    
    @pytest.mark.asyncio
    async def test_multiple_subscribers(self, event_bus):
        """Test multiple subscribers for the same event."""
        received_events = []
        
        class Handler1:
            @event_bus.subscribe("TestEvent")
            async def handle(self, event):
                received_events.append(f"handler1_{event.data}")
        
        class Handler2:
            @event_bus.subscribe("TestEvent")
            async def handle(self, event):
                received_events.append(f"handler2_{event.data}")
        
        # Subscribe handlers
        await event_bus.subscribe_tagged_methods(Handler1())
        await event_bus.subscribe_tagged_methods(Handler2())
        
        # Publish event
        test_event = TestEvent("test")
        await event_bus.publish(test_event)
        
        # Wait for processing
        await asyncio.sleep(0.1)
        
        # Verify both handlers received the event
        assert len(received_events) == 2
        assert "handler1_test" in received_events
        assert "handler2_test" in received_events
    
    @pytest.mark.asyncio
    async def test_priority_ordering(self, event_bus):
        """Test that higher priority subscribers execute first."""
        execution_order = []
        
        class LowPriorityHandler:
            @event_bus.subscribe("TestEvent", priority=1)
            async def handle(self, event):
                execution_order.append("low")
        
        class HighPriorityHandler:
            @event_bus.subscribe("TestEvent", priority=10)
            async def handle(self, event):
                execution_order.append("high")
        
        # Subscribe handlers
        await event_bus.subscribe_tagged_methods(LowPriorityHandler())
        await event_bus.subscribe_tagged_methods(HighPriorityHandler())
        
        # Publish event
        test_event = TestEvent("test")
        await event_bus.publish(test_event)
        
        # Wait for processing
        await asyncio.sleep(0.1)
        
        # Verify execution order (high priority first)
        assert execution_order == ["high", "low"]
    
    @pytest.mark.asyncio
    async def test_error_isolation(self, event_bus):
        """Test that errors in one handler don't affect others."""
        received_events = []
        
        class FailingHandler:
            @event_bus.subscribe("TestEvent")
            async def handle(self, event):
                raise Exception("Handler failed")
        
        class WorkingHandler:
            @event_bus.subscribe("TestEvent")
            async def handle(self, event):
                received_events.append(event.data)
        
        # Subscribe handlers
        await event_bus.subscribe_tagged_methods(FailingHandler())
        await event_bus.subscribe_tagged_methods(WorkingHandler())
        
        # Publish event
        test_event = TestEvent("test")
        await event_bus.publish(test_event)
        
        # Wait for processing
        await asyncio.sleep(0.1)
        
        # Verify working handler still received the event
        assert len(received_events) == 1
        assert received_events[0] == "test"
    
    @pytest.mark.asyncio
    async def test_send_method(self, event_bus):
        """Test the send method for RPC-style communication."""
        class ResponseHandler:
            @event_bus.subscribe("TestEvent")
            async def handle(self, event):
                return f"processed_{event.data}"
        
        # Subscribe handler
        await event_bus.subscribe_tagged_methods(ResponseHandler())
        
        # Send event and get response
        test_event = TestEvent("test")
        result = await event_bus.send("TestEvent", test_event)
        
        # Verify response
        assert result == "processed_test"
    
    @pytest.mark.asyncio
    async def test_clear_subscribers(self, event_bus):
        """Test clearing all subscribers."""
        received_events = []
        
        class TestHandler:
            @event_bus.subscribe("TestEvent")
            async def handle(self, event):
                received_events.append(event.data)
        
        # Subscribe handler
        await event_bus.subscribe_tagged_methods(TestHandler())
        
        # Clear subscribers
        await event_bus.clear_subscribers()
        
        # Publish event
        test_event = TestEvent("test")
        await event_bus.publish(test_event)
        
        # Wait for processing
        await asyncio.sleep(0.1)
        
        # Verify no events were received
        assert len(received_events) == 0
    
    @pytest.mark.asyncio
    async def test_message_received_event(self, event_bus):
        """Test with actual MessageReceived event."""
        received_events = []
        
        class MessageHandler:
            @event_bus.subscribe(MessageReceived)
            async def handle(self, event):
                received_events.append(event)
        
        # Subscribe handler
        await event_bus.subscribe_tagged_methods(MessageHandler())
        
        # Create and publish MessageReceived event
        message_event = MessageReceived(
            user_id=12345,
            chat_id=67890,
            message_id=1,
            message_text="Test message"
        )
        
        await event_bus.publish(message_event)
        
        # Wait for processing
        await asyncio.sleep(0.1)
        
        # Verify event was received
        assert len(received_events) == 1
        assert received_events[0].user_id == 12345
        assert received_events[0].message_text == "Test message"
