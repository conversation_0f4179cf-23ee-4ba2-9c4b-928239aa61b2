"""
Pytest configuration and fixtures for Zeitwahl AI Agent tests.
"""

import asyncio
import pytest
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime

from app.utils.event_bus import EventBus
from app.preprocess.message_validator import MessageValidator
from app.preprocess.user_identifier import UserIdentifier
from app.services.llm_service import LLMService
from app.services.calendar_service import CalendarService
from app.services.user_service import UserService


@pytest.fixture
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def event_bus():
    """Create a fresh event bus for testing."""
    bus = EventBus()
    yield bus
    await bus.clear_subscribers()


@pytest.fixture
def mock_telegram_message():
    """Create a mock Telegram message for testing."""
    message = MagicMock()
    message.from_user.id = 12345
    message.from_user.username = "testuser"
    message.from_user.first_name = "Test"
    message.from_user.last_name = "User"
    message.chat.id = 67890
    message.message_id = 1
    message.text = "Hello, schedule a meeting tomorrow at 2 PM"
    return message


@pytest.fixture
def sample_user_data():
    """Create sample user data for testing."""
    return {
        "user_id": 12345,
        "username": "testuser",
        "first_name": "Test",
        "last_name": "User",
        "timezone": "UTC",
        "preferences": {
            "default_event_duration": 60,
            "working_hours": {
                "start": "09:00",
                "end": "17:00",
                "days": ["monday", "tuesday", "wednesday", "thursday", "friday"]
            }
        },
        "integrations": {
            "google_calendar": {
                "enabled": True,
                "connected": True,
                "last_sync": datetime.now()
            }
        },
        "created_at": datetime.now(),
        "last_seen": datetime.now()
    }


@pytest.fixture
def sample_calendar_event():
    """Create sample calendar event data for testing."""
    return {
        "id": "test_event_123",
        "title": "Test Meeting",
        "start_time": "2024-01-15T14:00:00Z",
        "end_time": "2024-01-15T15:00:00Z",
        "description": "A test meeting",
        "attendees": ["<EMAIL>"],
        "status": "confirmed"
    }


@pytest.fixture
async def message_validator():
    """Create a message validator instance for testing."""
    return MessageValidator()


@pytest.fixture
async def user_identifier():
    """Create a user identifier instance for testing."""
    return UserIdentifier()


@pytest.fixture
async def llm_service():
    """Create an LLM service instance for testing."""
    return LLMService()


@pytest.fixture
async def calendar_service():
    """Create a calendar service instance for testing."""
    return CalendarService()


@pytest.fixture
async def user_service():
    """Create a user service instance for testing."""
    return UserService()


@pytest.fixture
def mock_llm_response():
    """Create a mock LLM response for testing."""
    return {
        "response": "I'd be happy to help you schedule a meeting! Let me create that for you.",
        "tool_calls": [
            {
                "name": "create_calendar_event",
                "parameters": {
                    "title": "Meeting",
                    "start_time": "2024-01-15T14:00:00Z",
                    "end_time": "2024-01-15T15:00:00Z",
                    "description": "Scheduled meeting"
                }
            }
        ],
        "tokens_used": 150,
        "processing_time": 0.5
    }


@pytest.fixture
def mock_tool_execution_result():
    """Create a mock tool execution result for testing."""
    return {
        "tool_name": "create_calendar_event",
        "execution_id": "exec_123",
        "success": True,
        "result": {
            "success": True,
            "event_id": "event_456",
            "message": "Successfully created event 'Meeting' for 2024-01-15T14:00:00Z"
        },
        "execution_time": 0.2
    }


# Test data for edge cases and failure scenarios
@pytest.fixture
def invalid_message_data():
    """Create invalid message data for testing error handling."""
    return [
        "",  # Empty message
        "x" * 5000,  # Too long message
        "<script>alert('xss')</script>",  # Potentially malicious content
        "a" * 100,  # Repetitive spam-like content
    ]


@pytest.fixture
def invalid_calendar_data():
    """Create invalid calendar event data for testing error handling."""
    return [
        {},  # Empty data
        {"title": ""},  # Missing required fields
        {"title": "Test", "start_time": "invalid-date"},  # Invalid date format
        {"title": "Test", "start_time": "2024-01-15T14:00:00Z", "end_time": "2024-01-15T13:00:00Z"},  # End before start
    ]


@pytest.fixture
def rate_limit_scenario():
    """Create data for testing rate limiting scenarios."""
    return {
        "user_id": 12345,
        "messages": ["message " + str(i) for i in range(15)],  # More than rate limit
        "time_window": 60  # seconds
    }
