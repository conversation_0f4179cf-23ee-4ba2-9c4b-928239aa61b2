from typing import List, Optional
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings


class TelegramConfig(BaseSettings):
    """Telegram bot configuration."""
    bot_token: str = Field(default="", description="Telegram bot token")
    webhook_url: Optional[str] = Field(default=None, description="Telegram webhook URL")
    webhook_secret: Optional[str] = Field(default=None, description="Telegram webhook secret")

    model_config = {"env_prefix": "TELEGRAM_"}


class LLMConfig(BaseSettings):
    """LLM service configuration."""
    primary_provider: str = Field(default="gemini", description="Primary LLM provider")
    fallback_providers: List[str] = Field(default=["deepseek"], description="Fallback LLM providers")

    # Gemini configuration
    gemini_api_key: Optional[str] = Field(default=None, description="Gemini API key")
    gemini_model: str = Field(default="gemini-pro", description="Gemini model name")

    # Deepseek configuration
    deepseek_api_key: Optional[str] = Field(default=None, description="Deepseek API key")
    deepseek_model: str = Field(default="deepseek-chat", description="Deepseek model name")
    deepseek_base_url: str = Field(default="https://api.deepseek.com", description="Deepseek API base URL")

    # OpenAI configuration (for testing)
    openai_api_key: Optional[str] = Field(default=None, description="OpenAI API key")
    openai_model: str = Field(default="gpt-3.5-turbo", description="OpenAI model name")

    max_tokens: int = Field(default=4000, description="Maximum tokens for LLM responses")
    temperature: float = Field(default=0.7, description="LLM temperature setting")
    timeout: int = Field(default=30, description="LLM request timeout in seconds")

    model_config = {"env_prefix": "LLM_"}


class DatabaseConfig(BaseSettings):
    """Database configuration."""
    mongodb_url: str = Field(default="mongodb://localhost:27017", description="MongoDB connection URL")
    database_name: str = Field(default="zeitwahl", description="Database name")

    # Redis configuration for caching
    redis_url: str = Field(default="redis://localhost:6379", description="Redis connection URL")
    redis_db: int = Field(default=0, description="Redis database number")
    cache_ttl: int = Field(default=3600, description="Cache TTL in seconds")  # 1 hour

    model_config = {"env_prefix": "DB_"}


class CalendarConfig(BaseSettings):
    """Calendar service configuration."""
    google_credentials_file: Optional[str] = Field(default=None, description="Google credentials JSON file path")
    google_client_id: Optional[str] = Field(default=None, description="Google OAuth client ID")
    google_client_secret: Optional[str] = Field(default=None, description="Google OAuth client secret")

    outlook_client_id: Optional[str] = Field(default=None, description="Outlook OAuth client ID")
    outlook_client_secret: Optional[str] = Field(default=None, description="Outlook OAuth client secret")
    outlook_tenant_id: Optional[str] = Field(default=None, description="Outlook tenant ID")

    default_timezone: str = Field(default="UTC", description="Default timezone")

    model_config = {"env_prefix": "CALENDAR_"}


class AppConfig(BaseSettings):
    """Main application configuration."""
    environment: str = Field(default="development", description="Application environment")
    debug: bool = Field(default=False, description="Debug mode")
    log_level: str = Field(default="INFO", description="Logging level")

    # Rate limiting
    rate_limit_requests: int = Field(default=10, description="Rate limit requests per window")
    rate_limit_window: int = Field(default=60, description="Rate limit window in seconds")

    # Security
    secret_key: str = Field(default="dev-secret-key", description="Application secret key")

    @field_validator("environment")
    @classmethod
    def validate_environment(cls, v: str) -> str:
        if v not in ["development", "staging", "production"]:
            raise ValueError("Environment must be development, staging, or production")
        return v

    @field_validator("log_level")
    @classmethod
    def validate_log_level(cls, v: str) -> str:
        if v not in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
            raise ValueError("Invalid log level")
        return v

    model_config = {"env_prefix": "APP_"}


class Settings:
    """Centralized settings management."""

    def __init__(self):
        # Load environment variables from .env file
        from dotenv import load_dotenv
        load_dotenv()

        # Initialize all configuration sections
        self.app = AppConfig()
        self.telegram = TelegramConfig()
        self.llm = LLMConfig()
        self.database = DatabaseConfig()
        self.calendar = CalendarConfig()

    @property
    def is_development(self) -> bool:
        return self.app.environment == "development"

    @property
    def is_production(self) -> bool:
        return self.app.environment == "production"

    def validate_required_settings(self) -> None:
        """Validate that all required settings are present."""
        errors = []

        # Check required Telegram settings
        if not self.telegram.bot_token or self.telegram.bot_token == "your-telegram-bot-token-here":
            errors.append("TELEGRAM_BOT_TOKEN is required")

        # Check that at least one LLM provider is configured
        llm_configured = False
        if self.llm.primary_provider == "gemini" and self.llm.gemini_api_key and self.llm.gemini_api_key != "your-gemini-api-key-here":
            llm_configured = True
        elif self.llm.primary_provider == "deepseek" and self.llm.deepseek_api_key and self.llm.deepseek_api_key != "your-deepseek-api-key-here":
            llm_configured = True
        elif self.llm.primary_provider == "openai" and self.llm.openai_api_key and self.llm.openai_api_key != "your-openai-api-key-here":
            llm_configured = True

        if not llm_configured:
            errors.append(f"LLM provider '{self.llm.primary_provider}' is not properly configured")

        if errors:
            raise ValueError(f"Configuration errors: {', '.join(errors)}")


# Global settings instance
settings = Settings()
