import logging
import re
from typing import Dict, List, Any

logger = logging.getLogger(__name__)


class ResponseValidator:
    """Validates LLM responses for safety, appropriateness, and quality."""
    
    def __init__(self):
        self.blocked_patterns = [
            r'<script.*?>.*?</script>',  # Script tags
            r'javascript:',  # JavaScript URLs
            r'data:.*?base64',  # Base64 data URLs
        ]
        
        self.inappropriate_keywords = [
            # Add inappropriate keywords here
            # This is a simplified list for demonstration
        ]
    
    async def validate_response(self, response_text: str, tool_calls: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Validate LLM response for safety and quality.
        
        Returns:
            Dict with validation results and sanitized response
        """
        result = {
            "is_valid": True,
            "sanitized_response": response_text,
            "quality_score": 0.0,
            "warnings": [],
            "errors": []
        }
        
        try:
            # Content safety checks
            if not self._check_content_safety(response_text):
                result["is_valid"] = False
                result["errors"].append("unsafe_content")
                return result
            
            # Sanitize content
            sanitized = self._sanitize_response(response_text)
            if sanitized != response_text:
                result["warnings"].append("content_sanitized")
                result["sanitized_response"] = sanitized
            
            # Validate tool calls
            tool_validation = await self._validate_tool_calls(tool_calls)
            if not tool_validation["is_valid"]:
                result["warnings"].append("invalid_tool_calls")
                # Don't mark as invalid, just warn
            
            # Calculate quality score
            result["quality_score"] = self._calculate_quality_score(sanitized, tool_calls)
            
            # Check minimum quality threshold
            if result["quality_score"] < 0.3:
                result["warnings"].append("low_quality_response")
            
            logger.debug(f"Response validation completed with score: {result['quality_score']}")
            
        except Exception as e:
            logger.error(f"Error validating response: {e}")
            result["is_valid"] = False
            result["errors"].append("validation_error")
        
        return result
    
    def _check_content_safety(self, text: str) -> bool:
        """Check if content is safe and appropriate."""
        text_lower = text.lower()
        
        # Check for inappropriate keywords
        for keyword in self.inappropriate_keywords:
            if keyword in text_lower:
                logger.warning(f"Inappropriate content detected: {keyword}")
                return False
        
        # Check for blocked patterns
        for pattern in self.blocked_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                logger.warning(f"Blocked pattern detected: {pattern}")
                return False
        
        return True
    
    def _sanitize_response(self, text: str) -> str:
        """Sanitize response content."""
        sanitized = text
        
        # Remove blocked patterns
        for pattern in self.blocked_patterns:
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)
        
        # Clean up excessive whitespace
        sanitized = re.sub(r'\s+', ' ', sanitized).strip()
        
        # Remove any remaining HTML tags (basic sanitization)
        sanitized = re.sub(r'<[^>]+>', '', sanitized)
        
        return sanitized
    
    async def _validate_tool_calls(self, tool_calls: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Validate tool calls for correctness."""
        result = {
            "is_valid": True,
            "errors": []
        }
        
        for tool_call in tool_calls:
            # Check required fields
            if "name" not in tool_call:
                result["is_valid"] = False
                result["errors"].append("missing_tool_name")
                continue
            
            if "parameters" not in tool_call:
                result["is_valid"] = False
                result["errors"].append("missing_tool_parameters")
                continue
            
            # Validate specific tool calls
            tool_name = tool_call["name"]
            parameters = tool_call["parameters"]
            
            if tool_name == "create_calendar_event":
                if not self._validate_calendar_event_params(parameters):
                    result["is_valid"] = False
                    result["errors"].append("invalid_calendar_event_params")
            
            elif tool_name == "get_calendar_events":
                if not self._validate_get_events_params(parameters):
                    result["is_valid"] = False
                    result["errors"].append("invalid_get_events_params")
        
        return result
    
    def _validate_calendar_event_params(self, params: Dict[str, Any]) -> bool:
        """Validate calendar event creation parameters."""
        required_fields = ["title", "start_time", "end_time"]
        
        for field in required_fields:
            if field not in params or not params[field]:
                return False
        
        # Basic date format validation (simplified)
        start_time = params.get("start_time", "")
        end_time = params.get("end_time", "")
        
        if not self._is_valid_datetime_format(start_time):
            return False
        
        if not self._is_valid_datetime_format(end_time):
            return False
        
        return True
    
    def _validate_get_events_params(self, params: Dict[str, Any]) -> bool:
        """Validate get calendar events parameters."""
        required_fields = ["start_date", "end_date"]
        
        for field in required_fields:
            if field not in params or not params[field]:
                return False
        
        return True
    
    def _is_valid_datetime_format(self, datetime_str: str) -> bool:
        """Check if datetime string is in valid ISO format."""
        # Simplified validation - in real implementation, use proper datetime parsing
        return len(datetime_str) > 10 and ("T" in datetime_str or " " in datetime_str)
    
    def _calculate_quality_score(self, text: str, tool_calls: List[Dict[str, Any]]) -> float:
        """Calculate response quality score (0.0 to 1.0)."""
        score = 0.0
        
        # Length check (not too short, not too long)
        text_length = len(text.strip())
        if 10 <= text_length <= 2000:
            score += 0.3
        elif text_length > 2000:
            score += 0.1  # Penalize very long responses
        
        # Coherence check (basic)
        sentences = text.split('.')
        if len(sentences) >= 2:
            score += 0.2
        
        # Helpfulness indicators
        helpful_phrases = [
            "i can help", "let me", "i'll", "would you like",
            "here's", "you can", "i suggest", "i recommend"
        ]
        
        text_lower = text.lower()
        helpful_count = sum(1 for phrase in helpful_phrases if phrase in text_lower)
        score += min(helpful_count * 0.1, 0.3)
        
        # Tool usage appropriateness
        if tool_calls:
            score += 0.2  # Bonus for using tools when appropriate
        
        return min(score, 1.0)
