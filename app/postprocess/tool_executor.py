import asyncio
import logging
import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime

from app.utils.event_bus import event_bus
from app.utils.events import ToolExecutionStarted, ToolExecutionCompleted

logger = logging.getLogger(__name__)


class ToolExecutor:
    """Executes tool calls from LLM responses."""
    
    def __init__(self):
        # In a real implementation, this would connect to actual services
        self.mock_calendar_events = []
    
    async def execute_tools(self, user_id: int, chat_id: int, message_id: int, 
                          tool_calls: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Execute a list of tool calls and return results.
        
        Returns:
            List of execution results
        """
        results = []
        
        for tool_call in tool_calls:
            try:
                execution_id = str(uuid.uuid4())
                tool_name = tool_call.get("name", "")
                parameters = tool_call.get("parameters", {})
                
                # Publish ToolExecutionStarted event
                start_event = ToolExecutionStarted(
                    user_id=user_id,
                    chat_id=chat_id,
                    message_id=message_id,
                    tool_name=tool_name,
                    tool_parameters=parameters,
                    execution_id=execution_id
                )
                await event_bus.publish(start_event)
                
                # Execute the tool
                start_time = datetime.now()
                result = await self._execute_single_tool(tool_name, parameters)
                end_time = datetime.now()
                execution_time = (end_time - start_time).total_seconds()
                
                # Publish ToolExecutionCompleted event
                completed_event = ToolExecutionCompleted(
                    user_id=user_id,
                    chat_id=chat_id,
                    message_id=message_id,
                    tool_name=tool_name,
                    execution_id=execution_id,
                    result=result,
                    success=True,
                    execution_time=execution_time
                )
                await event_bus.publish(completed_event)
                
                results.append({
                    "tool_name": tool_name,
                    "execution_id": execution_id,
                    "success": True,
                    "result": result,
                    "execution_time": execution_time
                })
                
                logger.info(f"Successfully executed tool: {tool_name}")
                
            except Exception as e:
                logger.error(f"Error executing tool {tool_name}: {e}")
                
                # Publish error event
                error_event = ToolExecutionCompleted(
                    user_id=user_id,
                    chat_id=chat_id,
                    message_id=message_id,
                    tool_name=tool_name,
                    execution_id=execution_id,
                    result=None,
                    success=False,
                    error_message=str(e),
                    execution_time=0.0
                )
                await event_bus.publish(error_event)
                
                results.append({
                    "tool_name": tool_name,
                    "execution_id": execution_id,
                    "success": False,
                    "error": str(e),
                    "execution_time": 0.0
                })
        
        return results
    
    async def _execute_single_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Any:
        """Execute a single tool call."""
        if tool_name == "create_calendar_event":
            return await self._create_calendar_event(parameters)
        
        elif tool_name == "get_calendar_events":
            return await self._get_calendar_events(parameters)
        
        elif tool_name == "update_calendar_event":
            return await self._update_calendar_event(parameters)
        
        elif tool_name == "delete_calendar_event":
            return await self._delete_calendar_event(parameters)
        
        elif tool_name == "find_available_time":
            return await self._find_available_time(parameters)
        
        else:
            raise ValueError(f"Unknown tool: {tool_name}")
    
    async def _create_calendar_event(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Create a calendar event (mock implementation)."""
        await asyncio.sleep(0.1)  # Simulate API call
        
        event_id = str(uuid.uuid4())
        event = {
            "id": event_id,
            "title": params.get("title", ""),
            "start_time": params.get("start_time", ""),
            "end_time": params.get("end_time", ""),
            "description": params.get("description", ""),
            "attendees": params.get("attendees", []),
            "created_at": datetime.now().isoformat(),
            "status": "confirmed"
        }
        
        # Store in mock database
        self.mock_calendar_events.append(event)
        
        logger.info(f"Created calendar event: {event['title']}")
        
        return {
            "success": True,
            "event_id": event_id,
            "message": f"Successfully created event '{event['title']}' for {event['start_time']}"
        }
    
    async def _get_calendar_events(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Get calendar events (mock implementation)."""
        await asyncio.sleep(0.1)  # Simulate API call
        
        start_date = params.get("start_date", "")
        end_date = params.get("end_date", "")
        
        # Filter mock events (simplified)
        filtered_events = [
            event for event in self.mock_calendar_events
            if start_date <= event["start_time"] <= end_date
        ]
        
        logger.info(f"Retrieved {len(filtered_events)} calendar events")
        
        return {
            "success": True,
            "events": filtered_events,
            "count": len(filtered_events),
            "message": f"Found {len(filtered_events)} events between {start_date} and {end_date}"
        }
    
    async def _update_calendar_event(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Update a calendar event (mock implementation)."""
        await asyncio.sleep(0.1)  # Simulate API call
        
        event_id = params.get("event_id", "")
        
        # Find event in mock database
        event = None
        for e in self.mock_calendar_events:
            if e["id"] == event_id:
                event = e
                break
        
        if not event:
            raise ValueError(f"Event not found: {event_id}")
        
        # Update event fields
        if "title" in params:
            event["title"] = params["title"]
        if "start_time" in params:
            event["start_time"] = params["start_time"]
        if "end_time" in params:
            event["end_time"] = params["end_time"]
        if "description" in params:
            event["description"] = params["description"]
        
        event["updated_at"] = datetime.now().isoformat()
        
        logger.info(f"Updated calendar event: {event['title']}")
        
        return {
            "success": True,
            "event_id": event_id,
            "message": f"Successfully updated event '{event['title']}'"
        }
    
    async def _delete_calendar_event(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Delete a calendar event (mock implementation)."""
        await asyncio.sleep(0.1)  # Simulate API call
        
        event_id = params.get("event_id", "")
        
        # Find and remove event from mock database
        event = None
        for i, e in enumerate(self.mock_calendar_events):
            if e["id"] == event_id:
                event = self.mock_calendar_events.pop(i)
                break
        
        if not event:
            raise ValueError(f"Event not found: {event_id}")
        
        logger.info(f"Deleted calendar event: {event['title']}")
        
        return {
            "success": True,
            "event_id": event_id,
            "message": f"Successfully deleted event '{event['title']}'"
        }
    
    async def _find_available_time(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Find available time slots (mock implementation)."""
        await asyncio.sleep(0.1)  # Simulate API call
        
        duration = params.get("duration", 60)  # minutes
        
        # Generate mock available slots
        now = datetime.now()
        available_slots = []
        
        for i in range(1, 8):  # Next 7 days
            slot_start = now.replace(hour=10, minute=0, second=0, microsecond=0)
            slot_start = slot_start.replace(day=now.day + i)
            slot_end = slot_start.replace(hour=slot_start.hour + 1)
            
            available_slots.append({
                "start_time": slot_start.isoformat(),
                "end_time": slot_end.isoformat(),
                "duration": duration,
                "available": True
            })
        
        logger.info(f"Found {len(available_slots)} available time slots")
        
        return {
            "success": True,
            "available_slots": available_slots[:5],  # Return top 5
            "count": len(available_slots),
            "message": f"Found {len(available_slots)} available time slots of {duration} minutes"
        }
