import logging
from typing import Dict, List, Any

from app.utils.event_bus import event_bus
from app.utils.events import LLMResponseReceived, ResponseReady, ErrorOccurred
from .response_validator import ResponseValidator
from .tool_executor import ToolExecutor

logger = logging.getLogger(__name__)


class Postprocessor:
    """Orchestrates the postprocessing pipeline for LLM responses."""
    
    def __init__(self):
        self.response_validator = ResponseValidator()
        self.tool_executor = ToolExecutor()
        
        # Subscribe to events
        event_bus.subscribe_tagged_methods(self)
    
    @event_bus.subscribe(LLMResponseReceived)
    async def handle_llm_response_received(self, event: LLMResponseReceived):
        """Handle LLMResponseReceived events and process them through the pipeline."""
        try:
            logger.info(f"Postprocessing LLM response for user {event.user_id}")
            
            # Step 1: Validate response
            validation_result = await self.response_validator.validate_response(
                event.response_text, event.tool_calls
            )
            
            if not validation_result["is_valid"]:
                await self._handle_validation_error(event, validation_result)
                return
            
            sanitized_response = validation_result["sanitized_response"]
            quality_score = validation_result["quality_score"]
            
            # Step 2: Execute tools if any
            tool_results = []
            if event.tool_calls:
                tool_results = await self.tool_executor.execute_tools(
                    event.user_id, event.chat_id, event.message_id, event.tool_calls
                )
            
            # Step 3: Build final response
            final_response = await self._build_final_response(
                sanitized_response, tool_results, quality_score
            )
            
            # Step 4: Publish ResponseReady event
            response_event = ResponseReady(
                user_id=event.user_id,
                chat_id=event.chat_id,
                message_id=event.message_id,
                response_text=final_response["text"],
                response_type=final_response["type"]
            )
            
            await event_bus.publish(response_event)
            logger.info(f"Published ResponseReady event for user {event.user_id}")
            
        except Exception as e:
            logger.error(f"Error in postprocessing pipeline: {e}")
            await self._handle_postprocessing_error(event, str(e))
    
    async def _build_final_response(self, response_text: str, tool_results: List[Dict[str, Any]], 
                                  quality_score: float) -> Dict[str, str]:
        """Build the final response combining LLM response and tool results."""
        try:
            final_text = response_text
            response_type = "text"
            
            # Add tool execution results to response
            if tool_results:
                tool_summaries = []
                
                for result in tool_results:
                    if result["success"]:
                        tool_name = result["tool_name"]
                        tool_result = result.get("result", {})
                        
                        if tool_name == "create_calendar_event":
                            message = tool_result.get("message", "Event created successfully")
                            tool_summaries.append(f"✅ {message}")
                        
                        elif tool_name == "get_calendar_events":
                            count = tool_result.get("count", 0)
                            tool_summaries.append(f"📅 Found {count} calendar events")
                        
                        elif tool_name == "find_available_time":
                            count = tool_result.get("count", 0)
                            tool_summaries.append(f"🕐 Found {count} available time slots")
                        
                        elif tool_name == "update_calendar_event":
                            message = tool_result.get("message", "Event updated successfully")
                            tool_summaries.append(f"✏️ {message}")
                        
                        elif tool_name == "delete_calendar_event":
                            message = tool_result.get("message", "Event deleted successfully")
                            tool_summaries.append(f"🗑️ {message}")
                        
                        else:
                            tool_summaries.append(f"✅ {tool_name} executed successfully")
                    
                    else:
                        error = result.get("error", "Unknown error")
                        tool_summaries.append(f"❌ Failed to execute {result['tool_name']}: {error}")
                
                # Append tool results to response
                if tool_summaries:
                    final_text += "\n\n" + "\n".join(tool_summaries)
            
            # Add quality indicators for low-quality responses
            if quality_score < 0.5:
                final_text += "\n\n_Note: I might not have fully understood your request. Please feel free to ask for clarification._"
            
            # Format response for better readability
            if len(final_text) > 500:
                response_type = "markdown"  # Use markdown for longer responses
            
            return {
                "text": final_text,
                "type": response_type
            }
            
        except Exception as e:
            logger.error(f"Error building final response: {e}")
            return {
                "text": response_text,  # Fallback to original response
                "type": "text"
            }
    
    async def _handle_validation_error(self, event: LLMResponseReceived, 
                                     validation_result: Dict[str, Any]):
        """Handle response validation errors."""
        errors = validation_result.get("errors", [])
        
        error_messages = {
            "unsafe_content": "I cannot provide that type of response. Please ask something else.",
            "validation_error": "There was an error processing the response. Please try again."
        }
        
        # Get the first error message
        error_type = errors[0] if errors else "validation_error"
        error_message = error_messages.get(error_type, error_messages["validation_error"])
        
        # Send a safe fallback response
        response_event = ResponseReady(
            user_id=event.user_id,
            chat_id=event.chat_id,
            message_id=event.message_id,
            response_text=error_message,
            response_type="text"
        )
        
        await event_bus.publish(response_event)
        
        # Also publish error event for logging
        error_event = ErrorOccurred(
            user_id=event.user_id,
            chat_id=event.chat_id,
            message_id=event.message_id,
            error_type="response_validation_error",
            error_message=f"Response validation failed: {', '.join(errors)}",
            component="postprocessor"
        )
        
        await event_bus.publish(error_event)
    
    async def _handle_postprocessing_error(self, event: LLMResponseReceived, error_msg: str):
        """Handle general postprocessing errors."""
        # Send a generic error response to user
        response_event = ResponseReady(
            user_id=event.user_id,
            chat_id=event.chat_id,
            message_id=event.message_id,
            response_text="I encountered an error while processing your request. Please try again.",
            response_type="text"
        )
        
        await event_bus.publish(response_event)
        
        # Publish error event for logging
        error_event = ErrorOccurred(
            user_id=event.user_id,
            chat_id=event.chat_id,
            message_id=event.message_id,
            error_type="postprocessing_error",
            error_message=error_msg,
            component="postprocessor"
        )
        
        await event_bus.publish(error_event)
