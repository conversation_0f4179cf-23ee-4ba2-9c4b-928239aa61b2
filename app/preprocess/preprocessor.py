import logging
from typing import Dict, List, Any

from app.utils.event_bus import event_bus
from app.utils.events import MessageReceived, MessagePreprocessed, ErrorOccurred
from .message_validator import MessageValidator
from .user_identifier import UserIdentifier
from .context_builder import Context<PERSON>uilder
from .prompt_builder import PromptBuilder

logger = logging.getLogger(__name__)


class Preprocessor:
    """Orchestrates the preprocessing pipeline for incoming messages."""
    
    def __init__(self):
        self.message_validator = MessageValidator()
        self.user_identifier = UserIdentifier()
        self.context_builder = ContextBuilder()
        self.prompt_builder = PromptBuilder()
        
        # Subscribe to events
        event_bus.subscribe_tagged_methods(self)
    
    @event_bus.subscribe(MessageReceived)
    async def handle_message_received(self, event: MessageReceived):
        """Handle MessageReceived events and process them through the pipeline."""
        try:
            logger.info(f"Processing message from user {event.user_id}")
            
            # Step 1: Validate message
            validation_result = await self.message_validator.validate_message(
                event.user_id, event.message_text
            )
            
            if not validation_result["is_valid"]:
                await self._handle_validation_error(event, validation_result)
                return
            
            sanitized_message = validation_result["sanitized_message"]
            
            # Step 2: Identify user and load context
            user_data = await self.user_identifier.identify_user(
                event.user_id, event.username, event.first_name, event.last_name
            )
            
            # Step 3: Get conversation history
            conversation_history = await self.user_identifier.get_conversation_history(
                event.user_id
            )
            
            # Step 4: Add current message to history
            await self.user_identifier.add_to_conversation_history(
                event.user_id, sanitized_message, "user"
            )
            
            # Step 5: Get user integrations
            integrations = await self.user_identifier.get_user_integrations(event.user_id)
            
            # Step 6: Build context
            context = await self.context_builder.build_context(
                user_data, conversation_history, integrations
            )
            
            # Step 7: Determine available tools based on integrations
            available_tools = self._get_available_tools(integrations)
            
            # Step 8: Build structured prompt
            processed_prompt = await self.prompt_builder.build_prompt(
                sanitized_message, context, available_tools
            )
            
            # Step 9: Publish MessagePreprocessed event
            preprocessed_event = MessagePreprocessed(
                user_id=event.user_id,
                chat_id=event.chat_id,
                message_id=event.message_id,
                original_message=event.message_text,
                processed_prompt=processed_prompt,
                user_context=context,
                conversation_history=conversation_history,
                available_tools=available_tools
            )
            
            await event_bus.publish(preprocessed_event)
            logger.info(f"Published MessagePreprocessed event for user {event.user_id}")
            
        except Exception as e:
            logger.error(f"Error in preprocessing pipeline: {e}")
            await self._handle_preprocessing_error(event, str(e))
    
    async def _handle_validation_error(self, event: MessageReceived, 
                                     validation_result: Dict[str, Any]):
        """Handle message validation errors."""
        errors = validation_result.get("errors", [])
        
        error_messages = {
            "rate_limit_exceeded": "You're sending messages too quickly. Please wait a moment.",
            "message_too_long": "Your message is too long. Please keep it under 4000 characters.",
            "empty_message": "Please send a text message.",
            "validation_error": "There was an error processing your message. Please try again."
        }
        
        # Get the first error message
        error_type = errors[0] if errors else "validation_error"
        error_message = error_messages.get(error_type, error_messages["validation_error"])
        
        # Publish error event
        error_event = ErrorOccurred(
            user_id=event.user_id,
            chat_id=event.chat_id,
            message_id=event.message_id,
            error_type="validation_error",
            error_message=error_message,
            component="preprocessor"
        )
        
        await event_bus.publish(error_event)
    
    async def _handle_preprocessing_error(self, event: MessageReceived, error_msg: str):
        """Handle general preprocessing errors."""
        error_event = ErrorOccurred(
            user_id=event.user_id,
            chat_id=event.chat_id,
            message_id=event.message_id,
            error_type="preprocessing_error",
            error_message="Failed to process your message. Please try again.",
            component="preprocessor"
        )
        
        await event_bus.publish(error_event)
    
    def _get_available_tools(self, integrations: Dict[str, Any]) -> List[str]:
        """Determine which tools are available based on user integrations."""
        available_tools = []
        
        # Check for calendar integrations
        for integration_name, config in integrations.items():
            if "calendar" in integration_name and config.get("connected", False):
                available_tools.extend([
                    "create_calendar_event",
                    "get_calendar_events",
                    "update_calendar_event",
                    "delete_calendar_event",
                    "find_available_time"
                ])
                break  # No need to check other calendar integrations
        
        # Always available tools (don't require integrations)
        available_tools.extend([
            "find_available_time"  # Can work with mock data
        ])
        
        # Remove duplicates
        return list(set(available_tools))
