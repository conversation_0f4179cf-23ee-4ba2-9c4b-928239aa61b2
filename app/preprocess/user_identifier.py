import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

logger = logging.getLogger(__name__)


class UserIdentifier:
    """Identifies users and loads their context and integrations."""
    
    def __init__(self):
        # In a real implementation, this would connect to MongoDB
        # For now, we'll use in-memory storage for simplicity
        self.user_cache: Dict[int, Dict[str, Any]] = {}
        self.conversation_cache: Dict[int, List[Dict[str, Any]]] = {}
    
    async def identify_user(self, user_id: int, username: Optional[str] = None, 
                          first_name: Optional[str] = None, 
                          last_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Identify user and load their context.
        
        Returns:
            Dict containing user profile, integrations, and preferences
        """
        try:
            # Check cache first
            if user_id in self.user_cache:
                user_data = self.user_cache[user_id]
                logger.debug(f"User {user_id} loaded from cache")
            else:
                # Create new user profile
                user_data = await self._create_user_profile(
                    user_id, username, first_name, last_name
                )
                self.user_cache[user_id] = user_data
                logger.info(f"Created new user profile for {user_id}")
            
            # Update last seen
            user_data["last_seen"] = datetime.now()
            
            return user_data
            
        except Exception as e:
            logger.error(f"Error identifying user {user_id}: {e}")
            # Return minimal user data on error
            return {
                "user_id": user_id,
                "username": username,
                "first_name": first_name,
                "last_name": last_name,
                "timezone": "UTC",
                "integrations": {},
                "preferences": {},
                "created_at": datetime.now(),
                "last_seen": datetime.now()
            }
    
    async def get_conversation_history(self, user_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent conversation history for the user."""
        try:
            if user_id not in self.conversation_cache:
                self.conversation_cache[user_id] = []
            
            # Return last N messages
            history = self.conversation_cache[user_id][-limit:]
            logger.debug(f"Retrieved {len(history)} messages for user {user_id}")
            
            return history
            
        except Exception as e:
            logger.error(f"Error getting conversation history for user {user_id}: {e}")
            return []
    
    async def add_to_conversation_history(self, user_id: int, message: str, 
                                        role: str = "user") -> None:
        """Add message to conversation history."""
        try:
            if user_id not in self.conversation_cache:
                self.conversation_cache[user_id] = []
            
            conversation_entry = {
                "role": role,
                "content": message,
                "timestamp": datetime.now()
            }
            
            self.conversation_cache[user_id].append(conversation_entry)
            
            # Keep only last 50 messages to prevent memory issues
            if len(self.conversation_cache[user_id]) > 50:
                self.conversation_cache[user_id] = self.conversation_cache[user_id][-50:]
            
            logger.debug(f"Added message to conversation history for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error adding to conversation history: {e}")
    
    async def get_user_integrations(self, user_id: int) -> Dict[str, Any]:
        """Get available integrations for the user."""
        try:
            user_data = await self.identify_user(user_id)
            integrations = user_data.get("integrations", {})
            
            # In a real implementation, this would check actual integration status
            # For now, return mock data
            available_integrations = {
                "google_calendar": integrations.get("google_calendar", {
                    "enabled": False,
                    "connected": False,
                    "last_sync": None
                }),
                "outlook_calendar": integrations.get("outlook_calendar", {
                    "enabled": False,
                    "connected": False,
                    "last_sync": None
                })
            }
            
            return available_integrations
            
        except Exception as e:
            logger.error(f"Error getting user integrations: {e}")
            return {}
    
    async def _create_user_profile(self, user_id: int, username: Optional[str], 
                                 first_name: Optional[str], 
                                 last_name: Optional[str]) -> Dict[str, Any]:
        """Create a new user profile."""
        return {
            "user_id": user_id,
            "username": username,
            "first_name": first_name,
            "last_name": last_name,
            "timezone": "UTC",  # Default timezone
            "integrations": {
                "google_calendar": {
                    "enabled": False,
                    "connected": False,
                    "credentials": None,
                    "last_sync": None
                },
                "outlook_calendar": {
                    "enabled": False,
                    "connected": False,
                    "credentials": None,
                    "last_sync": None
                }
            },
            "preferences": {
                "default_event_duration": 60,  # minutes
                "notification_preferences": {
                    "email": False,
                    "push": True
                },
                "working_hours": {
                    "start": "09:00",
                    "end": "17:00",
                    "days": ["monday", "tuesday", "wednesday", "thursday", "friday"]
                }
            },
            "created_at": datetime.now(),
            "last_seen": datetime.now()
        }
