import re
import logging
from typing import Dict, List, Optional
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class MessageValidator:
    """Validates and sanitizes incoming messages."""
    
    def __init__(self):
        self.rate_limits: Dict[int, List[datetime]] = {}
        self.blocked_patterns = [
            r'<script.*?>.*?</script>',  # Script tags
            r'javascript:',  # JavaScript URLs
            r'data:.*?base64',  # Base64 data URLs
        ]
    
    async def validate_message(self, user_id: int, message: str) -> Dict[str, any]:
        """
        Validate message for safety, rate limiting, and content filtering.
        
        Returns:
            Dict with validation results and sanitized message
        """
        result = {
            "is_valid": True,
            "sanitized_message": message,
            "warnings": [],
            "errors": []
        }
        
        try:
            # Rate limiting check
            if not self._check_rate_limit(user_id):
                result["is_valid"] = False
                result["errors"].append("rate_limit_exceeded")
                return result
            
            # Content length validation
            if len(message) > 4000:
                result["is_valid"] = False
                result["errors"].append("message_too_long")
                return result
            
            if len(message.strip()) == 0:
                result["is_valid"] = False
                result["errors"].append("empty_message")
                return result
            
            # Content safety checks
            sanitized = self._sanitize_content(message)
            if sanitized != message:
                result["warnings"].append("content_sanitized")
                result["sanitized_message"] = sanitized
            
            # Spam detection (basic)
            if self._is_spam(message):
                result["warnings"].append("potential_spam")
            
            logger.debug(f"Message validation completed for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error validating message: {e}")
            result["is_valid"] = False
            result["errors"].append("validation_error")
        
        return result
    
    def _check_rate_limit(self, user_id: int) -> bool:
        """Check if user is within rate limits."""
        now = datetime.now()
        window_start = now - timedelta(seconds=60)  # 1 minute window
        
        # Initialize user rate limit tracking
        if user_id not in self.rate_limits:
            self.rate_limits[user_id] = []
        
        # Clean old entries
        self.rate_limits[user_id] = [
            timestamp for timestamp in self.rate_limits[user_id]
            if timestamp > window_start
        ]
        
        # Check limit (10 messages per minute)
        if len(self.rate_limits[user_id]) >= 10:
            return False
        
        # Add current timestamp
        self.rate_limits[user_id].append(now)
        return True
    
    def _sanitize_content(self, message: str) -> str:
        """Remove potentially dangerous content."""
        sanitized = message
        
        # Remove blocked patterns
        for pattern in self.blocked_patterns:
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)
        
        # Remove excessive whitespace
        sanitized = re.sub(r'\s+', ' ', sanitized).strip()
        
        return sanitized
    
    def _is_spam(self, message: str) -> bool:
        """Basic spam detection."""
        # Check for excessive repetition
        words = message.lower().split()
        if len(words) > 5:
            unique_words = set(words)
            if len(unique_words) / len(words) < 0.3:  # Less than 30% unique words
                return True
        
        # Check for excessive caps
        if len(message) > 10:
            caps_ratio = sum(1 for c in message if c.isupper()) / len(message)
            if caps_ratio > 0.7:  # More than 70% caps
                return True
        
        # Check for excessive special characters
        special_chars = sum(1 for c in message if not c.isalnum() and not c.isspace())
        if special_chars > len(message) * 0.5:  # More than 50% special chars
            return True
        
        return False
