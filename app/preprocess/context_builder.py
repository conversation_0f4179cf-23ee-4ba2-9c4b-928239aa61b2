import logging
from typing import Dict, List, Any
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class ContextBuilder:
    """Builds context for LLM processing including conversation history and external data."""
    
    def __init__(self):
        pass
    
    async def build_context(self, user_data: Dict[str, Any], 
                          conversation_history: List[Dict[str, Any]],
                          integrations: Dict[str, Any]) -> Dict[str, Any]:
        """
        Build comprehensive context for LLM processing.
        
        Returns:
            Dict containing all relevant context information
        """
        try:
            context = {
                "user_info": await self._build_user_context(user_data),
                "conversation": await self._build_conversation_context(conversation_history),
                "calendar": await self._build_calendar_context(integrations),
                "temporal": await self._build_temporal_context(user_data.get("timezone", "UTC")),
                "integrations": await self._build_integration_context(integrations)
            }
            
            logger.debug(f"Built context for user {user_data.get('user_id')}")
            return context
            
        except Exception as e:
            logger.error(f"Error building context: {e}")
            return {
                "user_info": {},
                "conversation": {},
                "calendar": {},
                "temporal": {},
                "integrations": {}
            }
    
    async def _build_user_context(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Build user-specific context."""
        return {
            "user_id": user_data.get("user_id"),
            "name": self._get_user_display_name(user_data),
            "timezone": user_data.get("timezone", "UTC"),
            "preferences": user_data.get("preferences", {}),
            "working_hours": user_data.get("preferences", {}).get("working_hours", {}),
            "member_since": user_data.get("created_at"),
            "last_active": user_data.get("last_seen")
        }
    
    async def _build_conversation_context(self, history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Build conversation context from history."""
        if not history:
            return {
                "recent_messages": [],
                "message_count": 0,
                "topics": [],
                "last_interaction": None
            }
        
        # Format recent messages for LLM
        recent_messages = []
        for msg in history[-5:]:  # Last 5 messages
            recent_messages.append({
                "role": msg.get("role", "user"),
                "content": msg.get("content", ""),
                "timestamp": msg.get("timestamp")
            })
        
        # Extract topics (simple keyword extraction)
        topics = self._extract_topics(history)
        
        return {
            "recent_messages": recent_messages,
            "message_count": len(history),
            "topics": topics,
            "last_interaction": history[-1].get("timestamp") if history else None
        }
    
    async def _build_calendar_context(self, integrations: Dict[str, Any]) -> Dict[str, Any]:
        """Build calendar context from integrations."""
        # In a real implementation, this would fetch actual calendar data
        # For now, return mock data
        
        calendar_context = {
            "recent_events": [],
            "upcoming_events": [],
            "conflicts": [],
            "available_slots": []
        }
        
        # Check if any calendar is connected
        has_calendar = any(
            integration.get("connected", False) 
            for integration in integrations.values()
            if "calendar" in str(integration)
        )
        
        if has_calendar:
            # Mock some calendar data
            now = datetime.now()
            calendar_context.update({
                "recent_events": [
                    {
                        "title": "Team Meeting",
                        "start": (now - timedelta(hours=2)).isoformat(),
                        "end": (now - timedelta(hours=1)).isoformat(),
                        "status": "completed"
                    }
                ],
                "upcoming_events": [
                    {
                        "title": "Project Review",
                        "start": (now + timedelta(hours=2)).isoformat(),
                        "end": (now + timedelta(hours=3)).isoformat(),
                        "status": "scheduled"
                    }
                ],
                "available_slots": [
                    {
                        "start": (now + timedelta(hours=4)).isoformat(),
                        "end": (now + timedelta(hours=5)).isoformat(),
                        "duration": 60
                    }
                ]
            })
        
        return calendar_context
    
    async def _build_temporal_context(self, timezone: str) -> Dict[str, Any]:
        """Build temporal context (current time, date, etc.)."""
        now = datetime.now()
        
        return {
            "current_datetime": now.isoformat(),
            "current_date": now.date().isoformat(),
            "current_time": now.time().isoformat(),
            "day_of_week": now.strftime("%A"),
            "timezone": timezone,
            "week_start": (now - timedelta(days=now.weekday())).date().isoformat(),
            "week_end": (now + timedelta(days=6-now.weekday())).date().isoformat()
        }
    
    async def _build_integration_context(self, integrations: Dict[str, Any]) -> Dict[str, Any]:
        """Build context about available integrations."""
        connected_integrations = []
        available_integrations = []
        
        for name, config in integrations.items():
            if config.get("connected", False):
                connected_integrations.append({
                    "name": name,
                    "status": "connected",
                    "last_sync": config.get("last_sync")
                })
            else:
                available_integrations.append({
                    "name": name,
                    "status": "available"
                })
        
        return {
            "connected": connected_integrations,
            "available": available_integrations,
            "total_connected": len(connected_integrations)
        }
    
    def _get_user_display_name(self, user_data: Dict[str, Any]) -> str:
        """Get user's display name."""
        first_name = user_data.get("first_name", "")
        last_name = user_data.get("last_name", "")
        username = user_data.get("username", "")
        
        if first_name and last_name:
            return f"{first_name} {last_name}"
        elif first_name:
            return first_name
        elif username:
            return username
        else:
            return f"User {user_data.get('user_id', 'Unknown')}"
    
    def _extract_topics(self, history: List[Dict[str, Any]]) -> List[str]:
        """Extract topics from conversation history (simple keyword extraction)."""
        topics = set()
        
        # Common calendar-related keywords
        calendar_keywords = [
            "meeting", "appointment", "schedule", "calendar", "event",
            "reminder", "booking", "availability", "time", "date"
        ]
        
        for msg in history:
            content = msg.get("content", "").lower()
            for keyword in calendar_keywords:
                if keyword in content:
                    topics.add(keyword)
        
        return list(topics)[:5]  # Return top 5 topics
