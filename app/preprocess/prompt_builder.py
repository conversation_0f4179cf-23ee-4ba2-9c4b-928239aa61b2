import json
import logging
from typing import Dict, List, Any
from datetime import datetime

from app.config import SYSTEM_PROMPTS

logger = logging.getLogger(__name__)


class PromptBuilder:
    """Builds structured prompts for LLM processing."""
    
    def __init__(self):
        self.available_tools = [
            {
                "name": "create_calendar_event",
                "description": "Create a new calendar event",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "title": {"type": "string", "description": "Event title"},
                        "start_time": {"type": "string", "description": "Start time in ISO format"},
                        "end_time": {"type": "string", "description": "End time in ISO format"},
                        "description": {"type": "string", "description": "Event description"},
                        "attendees": {"type": "array", "items": {"type": "string"}, "description": "List of attendee emails"}
                    },
                    "required": ["title", "start_time", "end_time"]
                }
            },
            {
                "name": "get_calendar_events",
                "description": "Get calendar events for a specific time range",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "start_date": {"type": "string", "description": "Start date in ISO format"},
                        "end_date": {"type": "string", "description": "End date in ISO format"},
                        "calendar_type": {"type": "string", "enum": ["google", "outlook", "all"], "description": "Which calendar to query"}
                    },
                    "required": ["start_date", "end_date"]
                }
            },
            {
                "name": "find_available_time",
                "description": "Find available time slots for scheduling",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "duration": {"type": "integer", "description": "Duration in minutes"},
                        "start_date": {"type": "string", "description": "Start date to search from"},
                        "end_date": {"type": "string", "description": "End date to search until"},
                        "working_hours_only": {"type": "boolean", "description": "Only search during working hours"}
                    },
                    "required": ["duration"]
                }
            },
            {
                "name": "update_calendar_event",
                "description": "Update an existing calendar event",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "event_id": {"type": "string", "description": "Event ID to update"},
                        "title": {"type": "string", "description": "New event title"},
                        "start_time": {"type": "string", "description": "New start time in ISO format"},
                        "end_time": {"type": "string", "description": "New end time in ISO format"},
                        "description": {"type": "string", "description": "New event description"}
                    },
                    "required": ["event_id"]
                }
            },
            {
                "name": "delete_calendar_event",
                "description": "Delete a calendar event",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "event_id": {"type": "string", "description": "Event ID to delete"}
                    },
                    "required": ["event_id"]
                }
            }
        ]
    
    async def build_prompt(self, message: str, context: Dict[str, Any], 
                          available_integrations: List[str]) -> str:
        """
        Build a structured prompt for LLM processing.
        
        Returns:
            Formatted prompt string ready for LLM
        """
        try:
            # Filter tools based on available integrations
            filtered_tools = self._filter_tools_by_integrations(available_integrations)
            
            # Build the system prompt
            system_prompt = self._build_system_prompt(context, filtered_tools)
            
            # Build the user message with context
            user_message = self._build_user_message(message, context)
            
            # Combine into final prompt
            final_prompt = f"{system_prompt}\n\nUser: {user_message}"
            
            logger.debug("Built structured prompt for LLM processing")
            return final_prompt
            
        except Exception as e:
            logger.error(f"Error building prompt: {e}")
            # Return a basic prompt on error
            return f"{SYSTEM_PROMPTS['base_system']}\n\nUser: {message}"
    
    def _build_system_prompt(self, context: Dict[str, Any], tools: List[Dict[str, Any]]) -> str:
        """Build the system prompt with context and tools."""
        user_info = context.get("user_info", {})
        temporal = context.get("temporal", {})
        integrations = context.get("integrations", {})
        
        # Format base system prompt
        base_prompt = SYSTEM_PROMPTS["base_system"].format(
            current_datetime=temporal.get("current_datetime", datetime.now().isoformat()),
            user_timezone=user_info.get("timezone", "UTC")
        )
        
        # Add calendar context if available
        calendar_context = ""
        if context.get("calendar"):
            calendar_info = context["calendar"]
            available_integrations = [
                integration["name"] for integration in integrations.get("connected", [])
            ]
            
            calendar_context = SYSTEM_PROMPTS["calendar_context"].format(
                available_integrations=", ".join(available_integrations) if available_integrations else "None",
                recent_events=self._format_events(calendar_info.get("recent_events", [])),
                upcoming_events=self._format_events(calendar_info.get("upcoming_events", []))
            )
        
        # Add conversation context if available
        conversation_context = ""
        if context.get("conversation", {}).get("recent_messages"):
            conversation_history = self._format_conversation_history(
                context["conversation"]["recent_messages"]
            )
            conversation_context = SYSTEM_PROMPTS["conversation_context"].format(
                conversation_history=conversation_history
            )
        
        # Add tool definitions
        tool_context = ""
        if tools:
            tools_json = json.dumps(tools, indent=2)
            tool_context = SYSTEM_PROMPTS["tool_definitions"].format(tools=tools_json)
        
        # Combine all parts
        full_prompt = base_prompt
        if calendar_context:
            full_prompt += "\n\n" + calendar_context
        if conversation_context:
            full_prompt += "\n\n" + conversation_context
        if tool_context:
            full_prompt += "\n\n" + tool_context
        
        full_prompt += "\n\n" + SYSTEM_PROMPTS["error_handling"]
        
        return full_prompt
    
    def _build_user_message(self, message: str, context: Dict[str, Any]) -> str:
        """Build the user message with additional context if needed."""
        user_info = context.get("user_info", {})
        temporal = context.get("temporal", {})
        
        # Add temporal context to user message
        contextual_message = f"[Current time: {temporal.get('current_datetime', 'Unknown')}] {message}"
        
        return contextual_message
    
    def _filter_tools_by_integrations(self, available_integrations: List[str]) -> List[Dict[str, Any]]:
        """Filter available tools based on user's connected integrations."""
        if not available_integrations:
            # If no integrations, return only basic tools
            return [
                tool for tool in self.available_tools 
                if tool["name"] in ["find_available_time"]
            ]
        
        # Return all tools if user has calendar integrations
        calendar_integrations = [
            integration for integration in available_integrations 
            if "calendar" in integration.lower()
        ]
        
        if calendar_integrations:
            return self.available_tools
        else:
            return []
    
    def _format_events(self, events: List[Dict[str, Any]]) -> str:
        """Format calendar events for display in prompt."""
        if not events:
            return "No events"
        
        formatted_events = []
        for event in events[:5]:  # Limit to 5 events
            title = event.get("title", "Untitled")
            start = event.get("start", "Unknown time")
            status = event.get("status", "")
            
            formatted_events.append(f"- {title} at {start} ({status})")
        
        return "\n".join(formatted_events)
    
    def _format_conversation_history(self, messages: List[Dict[str, Any]]) -> str:
        """Format conversation history for display in prompt."""
        formatted_messages = []
        
        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")
            timestamp = msg.get("timestamp", "")
            
            # Truncate long messages
            if len(content) > 200:
                content = content[:200] + "..."
            
            formatted_messages.append(f"{role.capitalize()}: {content}")
        
        return "\n".join(formatted_messages)
