import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class UserService:
    """Enhanced user management with session tracking and analytics."""
    
    def __init__(self):
        # In a real implementation, this would connect to MongoDB
        # For now, we'll use in-memory storage for simplicity
        self.users: Dict[int, Dict[str, Any]] = {}
        self.sessions: Dict[int, Dict[str, Any]] = {}
        self.analytics: Dict[int, Dict[str, Any]] = {}
    
    async def create_user(self, user_id: int, username: Optional[str] = None,
                         first_name: Optional[str] = None, 
                         last_name: Optional[str] = None) -> Dict[str, Any]:
        """Create a new user profile."""
        user_data = {
            "user_id": user_id,
            "username": username,
            "first_name": first_name,
            "last_name": last_name,
            "timezone": "UTC",
            "preferences": {
                "default_event_duration": 60,  # minutes
                "notification_preferences": {
                    "email": False,
                    "push": True
                },
                "working_hours": {
                    "start": "09:00",
                    "end": "17:00",
                    "days": ["monday", "tuesday", "wednesday", "thursday", "friday"]
                },
                "language": "en",
                "date_format": "YYYY-MM-DD",
                "time_format": "24h"
            },
            "integrations": {
                "google_calendar": {
                    "enabled": False,
                    "connected": False,
                    "credentials": None,
                    "last_sync": None
                },
                "outlook_calendar": {
                    "enabled": False,
                    "connected": False,
                    "credentials": None,
                    "last_sync": None
                }
            },
            "created_at": datetime.now(),
            "last_seen": datetime.now(),
            "status": "active"
        }
        
        self.users[user_id] = user_data
        
        # Initialize analytics
        self.analytics[user_id] = {
            "total_messages": 0,
            "total_events_created": 0,
            "total_events_updated": 0,
            "total_events_deleted": 0,
            "total_queries": 0,
            "last_activity": datetime.now(),
            "session_count": 0,
            "average_session_duration": 0.0,
            "favorite_features": [],
            "error_count": 0
        }
        
        logger.info(f"Created user profile for {user_id}")
        return user_data
    
    async def get_user(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user profile by ID."""
        return self.users.get(user_id)
    
    async def update_user(self, user_id: int, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update user profile."""
        if user_id not in self.users:
            raise ValueError(f"User not found: {user_id}")
        
        user = self.users[user_id]
        
        # Update allowed fields
        allowed_fields = ["timezone", "preferences", "integrations", "status"]
        for field in allowed_fields:
            if field in updates:
                if field == "preferences" and isinstance(updates[field], dict):
                    # Merge preferences
                    user["preferences"].update(updates[field])
                elif field == "integrations" and isinstance(updates[field], dict):
                    # Merge integrations
                    user["integrations"].update(updates[field])
                else:
                    user[field] = updates[field]
        
        user["last_seen"] = datetime.now()
        
        logger.info(f"Updated user profile for {user_id}")
        return user
    
    async def start_session(self, user_id: int) -> Dict[str, Any]:
        """Start a new user session."""
        session_data = {
            "session_id": f"session_{user_id}_{int(datetime.now().timestamp())}",
            "user_id": user_id,
            "start_time": datetime.now(),
            "end_time": None,
            "message_count": 0,
            "events_created": 0,
            "events_updated": 0,
            "events_deleted": 0,
            "queries_made": 0,
            "errors_encountered": 0,
            "status": "active"
        }
        
        self.sessions[user_id] = session_data
        
        # Update analytics
        if user_id in self.analytics:
            self.analytics[user_id]["session_count"] += 1
            self.analytics[user_id]["last_activity"] = datetime.now()
        
        logger.info(f"Started session for user {user_id}")
        return session_data
    
    async def end_session(self, user_id: int) -> Optional[Dict[str, Any]]:
        """End user session and calculate metrics."""
        if user_id not in self.sessions:
            return None
        
        session = self.sessions[user_id]
        session["end_time"] = datetime.now()
        session["status"] = "ended"
        
        # Calculate session duration
        duration = (session["end_time"] - session["start_time"]).total_seconds()
        session["duration"] = duration
        
        # Update analytics
        if user_id in self.analytics:
            analytics = self.analytics[user_id]
            
            # Update average session duration
            total_sessions = analytics["session_count"]
            current_avg = analytics["average_session_duration"]
            new_avg = ((current_avg * (total_sessions - 1)) + duration) / total_sessions
            analytics["average_session_duration"] = new_avg
            
            # Update totals
            analytics["total_messages"] += session["message_count"]
            analytics["total_events_created"] += session["events_created"]
            analytics["total_events_updated"] += session["events_updated"]
            analytics["total_events_deleted"] += session["events_deleted"]
            analytics["total_queries"] += session["queries_made"]
            analytics["error_count"] += session["errors_encountered"]
        
        logger.info(f"Ended session for user {user_id}, duration: {duration:.2f}s")
        return session
    
    async def update_session_activity(self, user_id: int, activity_type: str) -> None:
        """Update session activity counters."""
        if user_id not in self.sessions:
            return
        
        session = self.sessions[user_id]
        
        if activity_type == "message":
            session["message_count"] += 1
        elif activity_type == "event_created":
            session["events_created"] += 1
        elif activity_type == "event_updated":
            session["events_updated"] += 1
        elif activity_type == "event_deleted":
            session["events_deleted"] += 1
        elif activity_type == "query":
            session["queries_made"] += 1
        elif activity_type == "error":
            session["errors_encountered"] += 1
    
    async def get_user_analytics(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user analytics and insights."""
        if user_id not in self.analytics:
            return None
        
        analytics = self.analytics[user_id].copy()
        
        # Add computed metrics
        if analytics["session_count"] > 0:
            analytics["messages_per_session"] = analytics["total_messages"] / analytics["session_count"]
            analytics["events_per_session"] = (
                analytics["total_events_created"] + 
                analytics["total_events_updated"] + 
                analytics["total_events_deleted"]
            ) / analytics["session_count"]
        else:
            analytics["messages_per_session"] = 0
            analytics["events_per_session"] = 0
        
        # Calculate engagement level
        if analytics["total_messages"] > 50:
            analytics["engagement_level"] = "high"
        elif analytics["total_messages"] > 10:
            analytics["engagement_level"] = "medium"
        else:
            analytics["engagement_level"] = "low"
        
        return analytics
    
    async def get_user_preferences(self, user_id: int) -> Dict[str, Any]:
        """Get user preferences."""
        user = await self.get_user(user_id)
        if not user:
            return {}
        
        return user.get("preferences", {})
    
    async def update_user_preferences(self, user_id: int, preferences: Dict[str, Any]) -> Dict[str, Any]:
        """Update user preferences."""
        return await self.update_user(user_id, {"preferences": preferences})
    
    async def get_user_integrations(self, user_id: int) -> Dict[str, Any]:
        """Get user integrations status."""
        user = await self.get_user(user_id)
        if not user:
            return {}
        
        return user.get("integrations", {})
    
    async def update_integration_status(self, user_id: int, integration_name: str, 
                                      status: Dict[str, Any]) -> Dict[str, Any]:
        """Update integration status for user."""
        user = await self.get_user(user_id)
        if not user:
            raise ValueError(f"User not found: {user_id}")
        
        if "integrations" not in user:
            user["integrations"] = {}
        
        if integration_name not in user["integrations"]:
            user["integrations"][integration_name] = {}
        
        user["integrations"][integration_name].update(status)
        user["last_seen"] = datetime.now()
        
        logger.info(f"Updated {integration_name} integration for user {user_id}")
        return user["integrations"][integration_name]
    
    async def get_active_users(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get users active within specified hours."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        active_users = []
        for user_id, user_data in self.users.items():
            if user_data.get("last_seen", datetime.min) > cutoff_time:
                active_users.append(user_data)
        
        return active_users
    
    async def cleanup_inactive_sessions(self, hours: int = 24) -> int:
        """Clean up inactive sessions older than specified hours."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        inactive_sessions = []
        for user_id, session in self.sessions.items():
            if session.get("start_time", datetime.now()) < cutoff_time and session.get("status") == "active":
                inactive_sessions.append(user_id)
        
        # End inactive sessions
        for user_id in inactive_sessions:
            await self.end_session(user_id)
        
        logger.info(f"Cleaned up {len(inactive_sessions)} inactive sessions")
        return len(inactive_sessions)
