import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from abc import ABC, abstractmethod

from app.config import settings

logger = logging.getLogger(__name__)


class CalendarProvider(ABC):
    """Abstract base class for calendar providers."""
    
    @abstractmethod
    async def create_event(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a calendar event."""
        pass
    
    @abstractmethod
    async def get_events(self, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """Get calendar events in date range."""
        pass
    
    @abstractmethod
    async def update_event(self, event_id: str, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update a calendar event."""
        pass
    
    @abstractmethod
    async def delete_event(self, event_id: str) -> bool:
        """Delete a calendar event."""
        pass
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Provider name."""
        pass


class MockCalendarProvider(CalendarProvider):
    """Mock calendar provider for testing and development."""
    
    def __init__(self):
        self.events: List[Dict[str, Any]] = []
    
    @property
    def name(self) -> str:
        return "mock"
    
    async def create_event(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a mock calendar event."""
        event = {
            "id": f"mock_event_{len(self.events) + 1}",
            "title": event_data.get("title", ""),
            "start_time": event_data.get("start_time", ""),
            "end_time": event_data.get("end_time", ""),
            "description": event_data.get("description", ""),
            "attendees": event_data.get("attendees", []),
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "status": "confirmed",
            "provider": "mock"
        }
        
        self.events.append(event)
        logger.info(f"Created mock calendar event: {event['title']}")
        
        return event
    
    async def get_events(self, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """Get mock calendar events."""
        # Simple filtering (in real implementation, would parse dates properly)
        filtered_events = [
            event for event in self.events
            if start_date <= event["start_time"] <= end_date
        ]
        
        logger.info(f"Retrieved {len(filtered_events)} mock calendar events")
        return filtered_events
    
    async def update_event(self, event_id: str, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update a mock calendar event."""
        event = None
        for e in self.events:
            if e["id"] == event_id:
                event = e
                break
        
        if not event:
            raise ValueError(f"Event not found: {event_id}")
        
        # Update fields
        for key, value in event_data.items():
            if key in ["title", "start_time", "end_time", "description", "attendees"]:
                event[key] = value
        
        event["updated_at"] = datetime.now().isoformat()
        
        logger.info(f"Updated mock calendar event: {event['title']}")
        return event
    
    async def delete_event(self, event_id: str) -> bool:
        """Delete a mock calendar event."""
        for i, event in enumerate(self.events):
            if event["id"] == event_id:
                deleted_event = self.events.pop(i)
                logger.info(f"Deleted mock calendar event: {deleted_event['title']}")
                return True
        
        return False


class GoogleCalendarProvider(CalendarProvider):
    """Google Calendar provider."""
    
    def __init__(self, credentials: Dict[str, Any]):
        self.credentials = credentials
    
    @property
    def name(self) -> str:
        return "google"
    
    async def create_event(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a Google Calendar event."""
        # In a real implementation, this would use the Google Calendar API
        logger.warning("Google Calendar API not implemented, using mock")
        mock_provider = MockCalendarProvider()
        return await mock_provider.create_event(event_data)
    
    async def get_events(self, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """Get Google Calendar events."""
        logger.warning("Google Calendar API not implemented, using mock")
        mock_provider = MockCalendarProvider()
        return await mock_provider.get_events(start_date, end_date)
    
    async def update_event(self, event_id: str, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update a Google Calendar event."""
        logger.warning("Google Calendar API not implemented, using mock")
        mock_provider = MockCalendarProvider()
        return await mock_provider.update_event(event_id, event_data)
    
    async def delete_event(self, event_id: str) -> bool:
        """Delete a Google Calendar event."""
        logger.warning("Google Calendar API not implemented, using mock")
        mock_provider = MockCalendarProvider()
        return await mock_provider.delete_event(event_id)


class OutlookCalendarProvider(CalendarProvider):
    """Outlook Calendar provider."""
    
    def __init__(self, credentials: Dict[str, Any]):
        self.credentials = credentials
    
    @property
    def name(self) -> str:
        return "outlook"
    
    async def create_event(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create an Outlook Calendar event."""
        # In a real implementation, this would use the Microsoft Graph API
        logger.warning("Outlook Calendar API not implemented, using mock")
        mock_provider = MockCalendarProvider()
        return await mock_provider.create_event(event_data)
    
    async def get_events(self, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """Get Outlook Calendar events."""
        logger.warning("Outlook Calendar API not implemented, using mock")
        mock_provider = MockCalendarProvider()
        return await mock_provider.get_events(start_date, end_date)
    
    async def update_event(self, event_id: str, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update an Outlook Calendar event."""
        logger.warning("Outlook Calendar API not implemented, using mock")
        mock_provider = MockCalendarProvider()
        return await mock_provider.update_event(event_id, event_data)
    
    async def delete_event(self, event_id: str) -> bool:
        """Delete an Outlook Calendar event."""
        logger.warning("Outlook Calendar API not implemented, using mock")
        mock_provider = MockCalendarProvider()
        return await mock_provider.delete_event(event_id)


class CalendarService:
    """Multi-provider calendar service with conflict detection and resolution."""
    
    def __init__(self):
        self.providers: Dict[str, CalendarProvider] = {}
        self.default_timezone = settings.calendar.default_timezone
        
        self._initialize_providers()
    
    def _initialize_providers(self):
        """Initialize available calendar providers."""
        # Mock provider (always available for testing)
        self.providers["mock"] = MockCalendarProvider()
        
        # Google Calendar provider
        if settings.calendar.google_credentials_file or settings.calendar.google_client_id:
            credentials = {
                "credentials_file": settings.calendar.google_credentials_file,
                "client_id": settings.calendar.google_client_id,
                "client_secret": settings.calendar.google_client_secret
            }
            self.providers["google"] = GoogleCalendarProvider(credentials)
        
        # Outlook Calendar provider
        if settings.calendar.outlook_client_id:
            credentials = {
                "client_id": settings.calendar.outlook_client_id,
                "client_secret": settings.calendar.outlook_client_secret,
                "tenant_id": settings.calendar.outlook_tenant_id
            }
            self.providers["outlook"] = OutlookCalendarProvider(credentials)
        
        logger.info(f"Initialized calendar providers: {list(self.providers.keys())}")
    
    async def create_event(self, provider_name: str, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a calendar event using specified provider."""
        if provider_name not in self.providers:
            raise ValueError(f"Unknown calendar provider: {provider_name}")
        
        provider = self.providers[provider_name]
        
        # Check for conflicts before creating
        conflicts = await self.check_conflicts(
            provider_name, 
            event_data.get("start_time", ""), 
            event_data.get("end_time", "")
        )
        
        if conflicts:
            logger.warning(f"Scheduling conflict detected for event: {event_data.get('title', '')}")
        
        return await provider.create_event(event_data)
    
    async def get_events(self, provider_name: str, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """Get calendar events from specified provider."""
        if provider_name not in self.providers:
            raise ValueError(f"Unknown calendar provider: {provider_name}")
        
        provider = self.providers[provider_name]
        return await provider.get_events(start_date, end_date)
    
    async def find_available_slots(self, provider_name: str, duration_minutes: int, 
                                 start_date: str, end_date: str, 
                                 working_hours_only: bool = True) -> List[Dict[str, Any]]:
        """Find available time slots for scheduling."""
        if provider_name not in self.providers:
            # Use mock provider as fallback
            provider_name = "mock"
        
        # Get existing events
        existing_events = await self.get_events(provider_name, start_date, end_date)
        
        # Generate available slots (simplified algorithm)
        available_slots = []
        
        # Parse dates (simplified - in real implementation, use proper datetime parsing)
        start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
        
        current_dt = start_dt
        while current_dt < end_dt:
            # Check if this slot conflicts with existing events
            slot_end = current_dt + timedelta(minutes=duration_minutes)
            
            has_conflict = False
            for event in existing_events:
                event_start = datetime.fromisoformat(event["start_time"].replace('Z', '+00:00'))
                event_end = datetime.fromisoformat(event["end_time"].replace('Z', '+00:00'))
                
                if (current_dt < event_end and slot_end > event_start):
                    has_conflict = True
                    break
            
            if not has_conflict:
                # Check working hours if required
                if not working_hours_only or self._is_working_hours(current_dt):
                    available_slots.append({
                        "start_time": current_dt.isoformat(),
                        "end_time": slot_end.isoformat(),
                        "duration": duration_minutes,
                        "available": True
                    })
            
            # Move to next hour
            current_dt += timedelta(hours=1)
        
        return available_slots[:10]  # Return top 10 slots
    
    async def check_conflicts(self, provider_name: str, start_time: str, end_time: str) -> List[Dict[str, Any]]:
        """Check for scheduling conflicts."""
        if provider_name not in self.providers:
            return []
        
        # Get events in the time range
        events = await self.get_events(provider_name, start_time, end_time)
        
        conflicts = []
        for event in events:
            event_start = event["start_time"]
            event_end = event["end_time"]
            
            # Check for overlap (simplified)
            if (start_time < event_end and end_time > event_start):
                conflicts.append({
                    "event_id": event["id"],
                    "title": event["title"],
                    "start_time": event_start,
                    "end_time": event_end,
                    "conflict_type": "overlap"
                })
        
        return conflicts
    
    def _is_working_hours(self, dt: datetime) -> bool:
        """Check if datetime falls within working hours."""
        # Simplified working hours check (9 AM - 5 PM, Monday-Friday)
        if dt.weekday() >= 5:  # Weekend
            return False
        
        if dt.hour < 9 or dt.hour >= 17:  # Outside 9-5
            return False
        
        return True
