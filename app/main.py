#!/usr/bin/env python3
"""
Zeitwahl AI Agent - Main Application Entry Point

This module orchestrates the entire event-driven system, initializing all components
and starting the Telegram bot with the complete processing pipeline.
"""

import asyncio
import logging
import signal
import sys
from typing import List

from app.config import settings
from app.utils.event_bus import event_bus
from app.bot import TelegramBot
from app.preprocess import Preprocessor
from app.services import LLMService, CalendarService, UserService
from app.postprocess import Postprocessor


class ZeitwählApp:
    """Main application class that orchestrates all components."""
    
    def __init__(self):
        self.telegram_bot: TelegramBot = None
        self.preprocessor: Preprocessor = None
        self.llm_service: LLMService = None
        self.calendar_service: CalendarService = None
        self.user_service: UserService = None
        self.postprocessor: Postprocessor = None
        self.running = False
        
        # Setup logging
        self._setup_logging()
        
        # Setup signal handlers
        self._setup_signal_handlers()
    
    def _setup_logging(self):
        """Configure logging for the application."""
        log_level = getattr(logging, settings.app.log_level.upper(), logging.INFO)
        
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                # In production, you might want to add file handlers
            ]
        )
        
        # Set specific log levels for noisy libraries
        logging.getLogger('aiogram').setLevel(logging.WARNING)
        logging.getLogger('httpx').setLevel(logging.WARNING)
        
        logger = logging.getLogger(__name__)
        logger.info(f"Logging configured at {settings.app.log_level} level")
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger = logging.getLogger(__name__)
            logger.info(f"Received signal {signum}, initiating graceful shutdown...")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def initialize(self):
        """Initialize all application components."""
        logger = logging.getLogger(__name__)
        logger.info("Initializing Zeitwahl AI Agent...")
        
        try:
            # Validate configuration
            await self._validate_configuration()
            
            # Initialize services (order matters for dependencies)
            logger.info("Initializing core services...")
            self.user_service = UserService()
            self.calendar_service = CalendarService()
            
            # Initialize processing pipeline
            logger.info("Initializing processing pipeline...")
            self.preprocessor = Preprocessor()
            self.llm_service = LLMService()
            self.postprocessor = Postprocessor()
            
            # Initialize Telegram bot (last, as it depends on other components)
            logger.info("Initializing Telegram bot...")
            self.telegram_bot = TelegramBot()
            
            # Wait a moment for all subscriptions to be registered
            await asyncio.sleep(0.1)
            
            logger.info("All components initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize application: {e}")
            raise
    
    async def _validate_configuration(self):
        """Validate critical configuration settings."""
        logger = logging.getLogger(__name__)
        
        # Check required settings
        if not settings.telegram.bot_token:
            raise ValueError("TELEGRAM_BOT_TOKEN is required")
        
        # Warn about missing optional settings
        if not settings.llm.gemini_api_key and not settings.llm.deepseek_api_key:
            logger.warning("No LLM API keys configured, using mock provider only")
        
        if not settings.calendar.google_client_id and not settings.calendar.outlook_client_id:
            logger.warning("No calendar integrations configured, using mock provider only")
        
        logger.info("Configuration validation completed")
    
    async def start(self):
        """Start the application."""
        logger = logging.getLogger(__name__)
        
        if self.running:
            logger.warning("Application is already running")
            return
        
        try:
            logger.info("Starting Zeitwahl AI Agent...")
            self.running = True
            
            # Start the Telegram bot
            await self.telegram_bot.start_polling()
            
        except Exception as e:
            logger.error(f"Failed to start application: {e}")
            await self.shutdown()
            raise
    
    async def shutdown(self):
        """Gracefully shutdown the application."""
        logger = logging.getLogger(__name__)
        
        if not self.running:
            return
        
        logger.info("Shutting down Zeitwahl AI Agent...")
        self.running = False
        
        try:
            # Stop Telegram bot
            if self.telegram_bot:
                await self.telegram_bot.stop()
            
            # Clean up services
            if self.user_service:
                # Clean up any active sessions
                await self.user_service.cleanup_inactive_sessions(0)  # Clean all sessions
            
            # Clear event bus subscriptions
            await event_bus.clear_subscribers()
            
            logger.info("Shutdown completed successfully")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    async def health_check(self) -> dict:
        """Perform application health check."""
        health_status = {
            "status": "healthy",
            "timestamp": asyncio.get_event_loop().time(),
            "components": {}
        }
        
        try:
            # Check Telegram bot
            if self.telegram_bot:
                health_status["components"]["telegram_bot"] = "healthy"
            else:
                health_status["components"]["telegram_bot"] = "not_initialized"
                health_status["status"] = "degraded"
            
            # Check services
            services = ["preprocessor", "llm_service", "postprocessor", "calendar_service", "user_service"]
            for service_name in services:
                service = getattr(self, service_name, None)
                if service:
                    health_status["components"][service_name] = "healthy"
                else:
                    health_status["components"][service_name] = "not_initialized"
                    health_status["status"] = "degraded"
            
            # Check event bus
            health_status["components"]["event_bus"] = "healthy"
            
        except Exception as e:
            health_status["status"] = "unhealthy"
            health_status["error"] = str(e)
        
        return health_status


async def main():
    """Main entry point for the application."""
    logger = logging.getLogger(__name__)
    
    try:
        # Create and initialize the application
        app = ZeitwählApp()
        await app.initialize()
        
        # Start the application
        logger.info("🚀 Zeitwahl AI Agent is starting...")
        logger.info(f"Environment: {settings.app.environment}")
        logger.info(f"Debug mode: {settings.app.debug}")
        
        await app.start()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Application failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Run the application
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nGracefully shutting down...")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)
