# Configuration Guide

This guide explains how to configure the Zeitwahl bot with all necessary settings and API tokens.

## Quick Setup

1. **Copy the environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit the `.env` file** with your actual configuration values

3. **Validate your configuration:**
   ```bash
   python scripts/validate_config.py
   ```

4. **Start the bot** once validation passes

## Required Configuration

### Telegram Bot Token

1. Message [@BotFather](https://t.me/BotFather) on Telegram
2. Create a new bot with `/newbot`
3. Copy the bot token and set it in your `.env` file:
   ```
   TELEGRAM_BOT_TOKEN=your-actual-bot-token-here
   ```

### LLM Provider (Choose One)

#### Option 1: Gemini (Recommended)
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create an API key
3. Configure in `.env`:
   ```
   LLM_PRIMARY_PROVIDER=gemini
   GEMINI_API_KEY=your-gemini-api-key-here
   ```

#### Option 2: Deepseek
1. Visit [Deepseek Platform](https://platform.deepseek.com/api_keys)
2. Create an API key
3. Configure in `.env`:
   ```
   LLM_PRIMARY_PROVIDER=deepseek
   DEEPSEEK_API_KEY=your-deepseek-api-key-here
   ```

#### Option 3: OpenAI
1. Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. Create an API key
3. Configure in `.env`:
   ```
   LLM_PRIMARY_PROVIDER=openai
   OPENAI_API_KEY=your-openai-api-key-here
   ```

## Optional Configuration

### Database Settings

The bot uses MongoDB for data storage and Redis for caching. Default settings work for local development:

```env
DB_MONGODB_URL=mongodb://localhost:27017
DB_DATABASE_NAME=zeitwahl
DB_REDIS_URL=redis://localhost:6379
```

### Calendar Integration

#### Google Calendar
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google Calendar API
4. Create OAuth 2.0 credentials
5. Configure in `.env`:
   ```
   CALENDAR_GOOGLE_CLIENT_ID=your-client-id
   CALENDAR_GOOGLE_CLIENT_SECRET=your-client-secret
   ```

#### Microsoft Outlook
1. Go to [Azure Portal](https://portal.azure.com/)
2. Register a new application
3. Configure API permissions for Calendar
4. Configure in `.env`:
   ```
   CALENDAR_OUTLOOK_CLIENT_ID=your-client-id
   CALENDAR_OUTLOOK_CLIENT_SECRET=your-client-secret
   CALENDAR_OUTLOOK_TENANT_ID=your-tenant-id
   ```

### Application Settings

```env
# Environment: development, staging, or production
APP_ENVIRONMENT=development

# Enable debug mode (true/false)
APP_DEBUG=true

# Logging level: DEBUG, INFO, WARNING, ERROR, CRITICAL
APP_LOG_LEVEL=INFO

# Rate limiting
APP_RATE_LIMIT_REQUESTS=10
APP_RATE_LIMIT_WINDOW=60

# Security (change in production!)
APP_SECRET_KEY=your-super-secret-key-here
```

## Environment-Specific Configuration

### Development
- Use the default `.env` settings
- Enable debug mode
- Use local databases

### Production
- Set `APP_ENVIRONMENT=production`
- Set `APP_DEBUG=false`
- Use strong `APP_SECRET_KEY`
- Configure production databases
- Set appropriate log levels

## Validation

Always run the configuration validator before starting the bot:

```bash
python scripts/validate_config.py
```

This will check:
- ✅ Required settings are present
- ✅ API keys are configured
- ✅ No example values remain
- ⚠️ Security warnings for production

## Troubleshooting

### Common Issues

1. **"TELEGRAM_BOT_TOKEN is not set"**
   - Make sure you copied `.env.example` to `.env`
   - Check that the token is correctly set without quotes

2. **"LLM provider not properly configured"**
   - Verify your API key is correct
   - Check that you're using the right provider name

3. **"Configuration errors"**
   - Run the validation script for detailed error messages
   - Check the `.env` file format (no spaces around `=`)

### Getting Help

If you encounter issues:
1. Run `python scripts/validate_config.py` for detailed diagnostics
2. Check the logs for error messages
3. Verify your API keys are valid and have proper permissions

## Security Notes

- Never commit `.env` files to version control
- Use strong secret keys in production
- Regularly rotate API keys
- Use environment-specific configurations
- Enable rate limiting in production
